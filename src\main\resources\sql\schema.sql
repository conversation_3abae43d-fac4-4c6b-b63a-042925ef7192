-- <PERSON><PERSON><PERSON> OAuth2 Client Credentials Service Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS caidao_auth DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE caidao_auth;

-- OAuth2 client details table (simplified for client credentials only)
CREATE TABLE IF NOT EXISTS oauth_client_details (
    client_id VARCHAR(256) PRIMARY KEY,
    client_secret VARCHAR(256) NOT NULL,
    scope VARCHAR(256),
    authorities VARCHAR(256) DEFAULT 'ROLE_CLIENT',
    access_token_validity INT DEFAULT 3600,
    client_name VARCHAR(100),
    client_description VARCHAR(500),
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN NOT NULL DEFAULT FALSE,
    INDEX idx_enabled (enabled),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Note: OAuth2 tokens are stored in Redis for better performance
-- No additional token tables needed for client credentials grant
