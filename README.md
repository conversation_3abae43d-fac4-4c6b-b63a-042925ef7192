# Caidao Open Auth Service

基于Spring Boot 2.1.0 + Spring Security OAuth2 + 阿里云KMS + 阿里云API网关的OAuth 2.0客户端凭证授权服务，采用DDD架构设计。

## 技术栈

- **Java 8**
- **Spring Boot 2.1.0**
- **Spring Framework 5.1.2**
- **Spring Security OAuth2**
- **MySQL 5.7**
- **PostgreSQL**
- **Redis** - Token存储
- **MyBatis Plus 3.5.1**
- **阿里云KMS** - 密钥管理和加密
- **阿里云API网关** - API管理和路由
- **JWT** - JSON Web Token
- **Maven** - 项目管理
- **DDD架构** - 领域驱动设计

## 功能特性

### OAuth 2.0 支持
- ✅ Client Credentials Grant (专注于应用间通信)
- ✅ JWT Token 增强
- ✅ Token 撤销和内省
- ✅ Token 有效性验证

### 安全特性
- ✅ 密码加密存储 (BCrypt)
- ✅ JWT 签名验证
- ✅ 阿里云KMS密钥管理
- ✅ 客户端凭证验证

### 客户端管理
- ✅ OAuth2客户端注册
- ✅ 客户端配置管理
- ✅ 作用域管理
- ✅ 客户端状态管理

### 云服务集成
- ✅ 阿里云KMS加密服务
- ✅ 阿里云API网关集成
- ✅ 云原生部署支持

### DDD架构
- ✅ 领域驱动设计
- ✅ 分层架构 (Application, Domain, Infrastructure, Interfaces)
- ✅ 清晰的职责分离
- ✅ 可维护性和可扩展性

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 5.0+

### 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE caidao_auth DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p caidao_auth < src/main/resources/sql/schema.sql
mysql -u root -p caidao_auth < src/main/resources/sql/data.sql
```

### 配置文件

修改 `src/main/resources/application.yml` 中的配置：

```yaml
spring:
  datasource:
    url: ***************************************
    username: your_db_username
    password: your_db_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password

aliyun:
  access-key-id: your_access_key_id
  access-key-secret: your_access_key_secret
  region-id: cn-hangzhou
  kms:
    key-id: your_kms_key_id
  api-gateway:
    group-id: your_api_gateway_group_id
```

### 启动应用

```bash
mvn clean install
mvn spring-boot:run
```

应用将在 `http://localhost:8080/auth` 启动。

## API 文档

### OAuth2 端点

| 端点 | 描述 |
|------|------|
| `POST /oauth/token` | 获取访问令牌 |
| `GET /oauth/authorize` | 授权端点 |
| `POST /oauth/revoke` | 撤销令牌 |
| `POST /oauth/introspect` | 令牌内省 |
| `GET /oauth/userinfo` | 获取用户信息 |

#### 默认OAuth2客户端
- **Web客户端**: `caidao-web-client` / `caidao-web-secret`
- **移动客户端**: `caidao-mobile-client` / `caidao-mobile-secret`
- **API客户端**: `caidao-api-client` / `caidao-api-secret`

## 使用示例

### 获取访问令牌 (Authorization Code)

1. 重定向用户到授权端点：
```
GET /oauth/authorize?response_type=code&client_id=caidao-web-client&redirect_uri=http://localhost:3000/callback&scope=read
```

2. 使用授权码获取令牌：
```bash
curl -X POST http://localhost:8080/auth/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=YOUR_CODE&client_id=caidao-web-client&client_secret=caidao-web-secret&redirect_uri=http://localhost:3000/callback"
```

### 获取访问令牌 (Password Grant)

```bash
curl -X POST http://localhost:8080/auth/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=testuser&password=user123&client_id=caidao-web-client&client_secret=caidao-web-secret&scope=read"
```

### 使用访问令牌访问受保护资源

```bash
curl -X GET http://localhost:8080/auth/api/oauth2/userinfo \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t caidao-auth-service .

# 运行容器
docker run -d \
  --name caidao-auth \
  -p 8080:8080 \
  -e DB_HOST=your_db_host \
  -e DB_USERNAME=your_db_username \
  -e DB_PASSWORD=your_db_password \
  -e REDIS_HOST=your_redis_host \
  -e ALIYUN_ACCESS_KEY_ID=your_access_key_id \
  -e ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret \
  caidao-auth-service
```

### 阿里云部署

1. 配置阿里云KMS密钥
2. 配置阿里云API网关
3. 部署到阿里云ECS或容器服务

## 监控和日志

- **健康检查**: `/actuator/health`
- **指标监控**: `/actuator/metrics`
- **日志文件**: `logs/caidao-auth.log`
- **Druid监控**: `/druid/index.html` (admin/admin123)

## 安全建议

1. 修改默认用户密码
2. 使用HTTPS部署
3. 定期轮换JWT签名密钥
4. 配置适当的CORS策略
5. 启用访问日志和审计
6. 使用阿里云KMS管理敏感密钥

## 许可证

MIT License

## 联系方式

- 项目地址: https://github.com/caidao/caidao-open-auth-service
- 问题反馈: https://github.com/caidao/caidao-open-auth-service/issues
