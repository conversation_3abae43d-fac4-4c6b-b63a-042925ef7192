package com.caidaocloud.open.auth.service.infrastructure.config.jwt;

import com.caidaocloud.open.auth.service.infrastructure.config.oauth.SimpleJwtAccessTokenConverter;
import com.caidaocloud.security.dto.TokenDto;
import org.junit.Test;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * JWT访问令牌转换器测试
 *
 * <AUTHOR>
 */
public class JwtAccessTokenConverterTest {

    @Test
    public void testSimpleJwtAccessTokenConverter() {
        // 创建简单JWT访问令牌转换器实例
        SimpleJwtAccessTokenConverter converter = new SimpleJwtAccessTokenConverter("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...");
        
        // 准备测试数据
        DefaultOAuth2AccessToken accessToken = new DefaultOAuth2AccessToken("test-token");
        Map<String, Object> additionalInfo = new HashMap<>();
        additionalInfo.put("userid", "12345");
        additionalInfo.put("belongId", "tenant-001");
        accessToken.setAdditionalInformation(additionalInfo);

        // 创建模拟的OAuth2Authentication
        OAuth2Authentication authentication = mock(OAuth2Authentication.class);
        OAuth2Request oAuth2Request = mock(OAuth2Request.class);
        when(authentication.getOAuth2Request()).thenReturn(oAuth2Request);
        when(oAuth2Request.getClientId()).thenReturn("test-client");

        // 测试编码
        String jwt = converter.encode(accessToken, authentication);
        assertNotNull(jwt);
        assertFalse(jwt.isEmpty());
        
        // JWT应该包含三个部分，用.分隔
        String[] parts = jwt.split("\\.");
        assertEquals("JWT should have 3 parts separated by dots", 3, parts.length);

        // 测试解码
        Map<String, Object> decoded = converter.decode(jwt);
        assertNotNull(decoded);
        assertEquals("12345", decoded.get("userId"));
        assertEquals("tenant-001", decoded.get("tenantId"));

        // 验证转换器类型
        assertEquals("simple", converter.getSignerType());

        System.out.println("Simple JWT Access Token Converter test passed");
        System.out.println("Generated JWT: " + jwt);
    }

    @Test
    public void testTokenDtoMapping() {
        // 测试TokenDto的映射功能
        TokenDto tokenDto = new TokenDto("user123", "tenant456", 2);
        Map<String, Object> map = tokenDto.getMap();
        
        assertNotNull(map);
        assertEquals("user123", map.get("userId"));
        assertEquals("tenant456", map.get("tenantId"));
        assertEquals(2, map.get("type"));
        
        System.out.println("TokenDto mapping test passed");
    }
}
