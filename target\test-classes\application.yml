server:
  port: 8080

# JWT签名配置
jwt:
  signer:
    # 签名方式选择: simple(简单对称加密) 或 aliyun-kms(阿里云KMS)
    type: simple
    # 简单对称加密配置
    simple:
      secret-key: ${JWT_SECRET_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn...}

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:postgresql://***************:5432/caidaocloud_old?currentSchema=user_center&stringtype=unspecified
    username: postgres
    password: C<PERSON>ao2022
    driver-class-name: org.postgresql.Driver
  hikari:
    connection-timeout: 60000
    maximum-pool-size: 50
    validation-timeout: 3000
    idle-timeount: 60000
    max-lifetime: 60000
    login-timeount: 5
  redis:
    host: ***************
    port: 6379
    password: myredis
    database: 1
    timeout: 6000
    pool:
      max-idle: 20
      min-idle: 10
      max-active: 50
      max-wait: 5000
      test-on-borrow: true
  application:
    name: caidao-open-auth-service
# Alibaba Cloud Configuration (当jwt.signer.type=aliyun-kms时需要配置)
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
  region-id: ${ALIYUN_REGION_ID:cn-hangzhou}
  endpoint: ${ALIYUN_KMS_ENDPOINT:kms.cn-hangzhou.aliyuncs.com}
  group-id: ${ALIYUN_API_GATEWAY_GROUP_ID:your-group-id}

  # KMS Configuration
  kms:
    key-id: ${ALIYUN_KMS_KEY_ID:your-kms-key-id}
    key-version-id: ${ALIYUN_KMS_KEY_VERSION_ID:your-key-version-id}
    alg: ${ALIYUN_KMS_ALGORITHM:RSA_PKCS1_SHA_256}