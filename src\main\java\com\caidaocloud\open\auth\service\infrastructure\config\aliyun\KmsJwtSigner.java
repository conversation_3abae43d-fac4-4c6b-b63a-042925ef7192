package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import java.util.Base64;
import java.util.Map;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.AsymmetricSignRequest;
import com.aliyun.kms20160120.models.AsymmetricSignResponse;
import com.aliyun.kms20160120.models.AsymmetricVerifyResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.caidaocloud.util.FastjsonUtil;
import io.jsonwebtoken.JwsHeader;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KmsJwtSigner {

    private final Client kmsClient;
    @NacosValue(value = "${aliyun.kms.key-id}", autoRefreshed = true)
    private String keyId;
    @NacosValue(value = "${aliyun.kms.key-version-id}", autoRefreshed = true)
    private String keyVersionId;
    @NacosValue(value = "${aliyun.kms.alg}", autoRefreshed = true)
    private String alg;

    public KmsJwtSigner(AliyunConfig aliyunConfig) throws Exception {
        Config config = new Config()
                .setAccessKeyId(aliyunConfig.getAccessKeyId())
                .setAccessKeySecret(aliyunConfig.getAccessKeySecret())
                .setEndpoint(aliyunConfig.getEndpoint());
        this.kmsClient = new Client(config);
    }

    /**
     * 通用KMS操作执行器
     *
     * @param operation     KMS操作的函数式接口
     * @param operationName 操作名称，用于日志记录
     * @param <T>           返回值类型
     * @return 操作结果
     */
    private <T> T executeKmsOperation(KmsOperation<T> operation, String operationName) {
        try {
            log.debug("执行KMS操作: {}", operationName);
            T result = operation.execute();
            log.debug("KMS操作成功: {}", operationName);
            return result;
        } catch (TeaException error) {
            log.error("KMS操作失败: {}, 错误信息: {}", operationName, error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断建议: {}", error.getData().get("Recommend"));
            }
            throw new KmsOperationException("KMS操作失败: " + operationName, error);
        } catch (Exception error) {
            log.error("KMS操作异常: {}, 错误信息: {}", operationName, error.getMessage());
            throw new KmsOperationException("KMS操作异常: " + operationName, error);
        }
    }

    /**
     * 非对称签名
     *
     * @param digest 待签名的摘要
     * @return 签名结果
     */
    private String sign(String digest) {
        return executeKmsOperation(() -> {
            AsymmetricSignRequest request = new AsymmetricSignRequest()
                    .setKeyId(keyId)
                    .setKeyVersionId(keyVersionId)
                    .setAlgorithm(alg)
                    .setDigest(digest);
            kmsClient.execute()
            AsymmetricSignResponse response = kmsClient.asymmetricSign(request);
            return response.getBody().value;
        }, "非对称签名");
    }

    /**
     * 非对称验签
     *
     * @param keyId        密钥ID
     * @param keyVersionId 密钥版本ID
     * @param alg          算法
     * @param digest       摘要
     * @param signature    签名值
     * @return 验签结果
     */
    private boolean verify(String keyId, String keyVersionId, String alg, String digest, String signature) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.AsymmetricVerifyRequest request = new com.aliyun.kms20160120.models.AsymmetricVerifyRequest()
                    .setKeyId(keyId)
                    .setKeyVersionId(keyVersionId)
                    .setAlgorithm(alg)
                    .setDigest(digest)
                    .setValue(signature);

            AsymmetricVerifyResponse response = kmsClient.asymmetricVerify(request);
            return response.getBody().value;
        }, "非对称验签");
    }

    /**
     * KMS操作函数式接口
     *
     * @param <T> 返回值类型
     */
    @FunctionalInterface
    private interface KmsOperation<T> {
        T execute() throws Exception;
    }

    /**
     * KMS操作异常
     */
    public static class KmsOperationException extends RuntimeException {
        public KmsOperationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public String sign(Map<String, Object> claims) {
        String json = FastjsonUtil.toJson(claims);
        String payload = Base64.getUrlEncoder().withoutPadding().encodeToString(json.getBytes());
        return sign(payload);
    }

    public boolean verify(JwsHeader<?> header, String payload, String signature) {
        String keyId = header.getKeyId();
        String algorithm = header.getAlgorithm();
        String keyVersionId = (String) header.get("keyVersionId");
        return verify(keyId, keyVersionId, algorithm, payload, signature);
    }

    // ========== 扩展方法：支持其他KMS操作 ==========

    /**
     * 加密数据
     *
     * @param plaintext 明文数据
     * @return 加密后的数据
     */
    public String encrypt(String plaintext) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.EncryptRequest request = new com.aliyun.kms20160120.models.EncryptRequest()
                    .setKeyId(keyId)
                    .setPlaintext(plaintext);

            com.aliyun.kms20160120.models.EncryptResponse response = kmsClient.encrypt(request);
            return response.getBody().ciphertextBlob;
        }, "数据加密");
    }

    /**
     * 解密数据
     *
     * @param ciphertext 密文数据
     * @return 解密后的数据
     */
    public String decrypt(String ciphertext) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.DecryptRequest request = new com.aliyun.kms20160120.models.DecryptRequest()
                    .setCiphertextBlob(ciphertext);

            com.aliyun.kms20160120.models.DecryptResponse response = kmsClient.decrypt(request);
            return response.getBody().plaintext;
        }, "数据解密");
    }

    /**
     * 生成数据密钥
     *
     * @param keySpec 密钥规格
     * @return 数据密钥信息
     */
    public DataKeyInfo generateDataKey(String keySpec) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.GenerateDataKeyRequest request = new com.aliyun.kms20160120.models.GenerateDataKeyRequest()
                    .setKeyId(keyId)
                    .setKeySpec(keySpec);

            com.aliyun.kms20160120.models.GenerateDataKeyResponse response = kmsClient.generateDataKey(request);
            return new DataKeyInfo(
                    response.getBody().plaintext,
                    response.getBody().ciphertextBlob);
        }, "生成数据密钥");
    }

    /**
     * 数据密钥信息
     */
    public static class DataKeyInfo {
        private final String plaintextKey;
        private final String ciphertextBlob;

        public DataKeyInfo(String plaintextKey, String ciphertextBlob) {
            this.plaintextKey = plaintextKey;
            this.ciphertextBlob = ciphertextBlob;
        }

        public String getPlaintextKey() {
            return plaintextKey;
        }

        public String getCiphertextBlob() {
            return ciphertextBlob;
        }
    }
}