package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import java.util.Base64;
import java.util.Map;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.AsymmetricSignRequest;
import com.aliyun.kms20160120.models.AsymmetricSignResponse;
import com.aliyun.kms20160120.models.AsymmetricVerifyResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.caidaocloud.util.FastjsonUtil;
import java.lang.reflect.Method;
import io.jsonwebtoken.JwsHeader;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KmsJwtSigner {

    private final Client kmsClient;
    @NacosValue(value = "${aliyun.kms.key-id}", autoRefreshed = true)
    private String keyId;
    @NacosValue(value = "${aliyun.kms.key-version-id}", autoRefreshed = true)
    private String keyVersionId;
    @NacosValue(value = "${aliyun.kms.alg}", autoRefreshed = true)
    private String alg;

    public KmsJwtSigner(AliyunConfig aliyunConfig) throws Exception {
        Config config = new Config()
                .setAccessKeyId(aliyunConfig.getAccessKeyId())
                .setAccessKeySecret(aliyunConfig.getAccessKeySecret())
                .setEndpoint(aliyunConfig.getEndpoint());
        this.kmsClient = new Client(config);
    }

    /**
     * 通用KMS请求包装类
     */
    public abstract static class KmsRequestWrapper<T> {
        protected final String operationName;

        public KmsRequestWrapper(String operationName) {
            this.operationName = operationName;
        }

        /**
         * 构建请求对象
         */
        protected abstract Object buildRequest();

        /**
         * 获取SDK方法名
         */
        protected abstract String getMethodName();

        /**
         * 处理响应结果
         */
        protected abstract T processResponse(Object response);

        /**
         * 执行KMS操作
         */
        public T execute(Client kmsClient) throws Exception {
            Object request = buildRequest();
            String methodName = getMethodName();

            // 使用反射调用对应的SDK方法
            Method method = kmsClient.getClass().getMethod(methodName, request.getClass());
            Object response = method.invoke(kmsClient, request);

            return processResponse(response);
        }

        public String getOperationName() {
            return operationName;
        }
    }

    /**
     * 通用KMS操作执行器
     *
     * @param requestWrapper KMS请求包装器
     * @param <T>            返回值类型
     * @return 操作结果
     */
    private <T> T executeKmsOperation(KmsRequestWrapper<T> requestWrapper) {
        try {
            log.debug("执行KMS操作: {}", requestWrapper.getOperationName());
            T result = requestWrapper.execute(kmsClient);
            log.debug("KMS操作成功: {}", requestWrapper.getOperationName());
            return result;
        } catch (TeaException error) {
            log.error("KMS操作失败: {}, 错误信息: {}", requestWrapper.getOperationName(), error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断建议: {}", error.getData().get("Recommend"));
            }
            throw new KmsOperationException("KMS操作失败: " + requestWrapper.getOperationName(), error);
        } catch (Exception error) {
            log.error("KMS操作异常: {}, 错误信息: {}", requestWrapper.getOperationName(), error.getMessage());
            throw new KmsOperationException("KMS操作异常: " + requestWrapper.getOperationName(), error);
        }
    }

    /**
     * 非对称签名请求包装器
     */
    private class AsymmetricSignRequestWrapper extends KmsRequestWrapper<String> {
        private final String digest;

        public AsymmetricSignRequestWrapper(String digest) {
            super("非对称签名");
            this.digest = digest;
        }

        @Override
        protected Object buildRequest() {
            return new AsymmetricSignRequest()
                    .setKeyId(keyId)
                    .setKeyVersionId(keyVersionId)
                    .setAlgorithm(alg)
                    .setDigest(digest);
        }

        @Override
        protected String getMethodName() {
            return "asymmetricSign";
        }

        @Override
        protected String processResponse(Object response) {
            AsymmetricSignResponse signResponse = (AsymmetricSignResponse) response;
            return signResponse.getBody().value;
        }
    }

    /**
     * 非对称验签请求包装器
     */
    private class AsymmetricVerifyRequestWrapper extends KmsRequestWrapper<Boolean> {
        private final String keyId;
        private final String keyVersionId;
        private final String alg;
        private final String digest;
        private final String signature;

        public AsymmetricVerifyRequestWrapper(String keyId, String keyVersionId, String alg, String digest,
                String signature) {
            super("非对称验签");
            this.keyId = keyId;
            this.keyVersionId = keyVersionId;
            this.alg = alg;
            this.digest = digest;
            this.signature = signature;
        }

        @Override
        protected Object buildRequest() {
            return new com.aliyun.kms20160120.models.AsymmetricVerifyRequest()
                    .setKeyId(keyId)
                    .setKeyVersionId(keyVersionId)
                    .setAlgorithm(alg)
                    .setDigest(digest)
                    .setValue(signature);
        }

        @Override
        protected String getMethodName() {
            return "asymmetricVerify";
        }

        @Override
        protected Boolean processResponse(Object response) {
            AsymmetricVerifyResponse verifyResponse = (AsymmetricVerifyResponse) response;
            return verifyResponse.getBody().value;
        }
    }

    /**
     * 非对称签名
     *
     * @param digest 待签名的摘要
     * @return 签名结果
     */
    private String sign(String digest) {
        return executeKmsOperation(new AsymmetricSignRequestWrapper(digest));
    }

    /**
     * 非对称验签
     *
     * @param keyId        密钥ID
     * @param keyVersionId 密钥版本ID
     * @param alg          算法
     * @param digest       摘要
     * @param signature    签名值
     * @return 验签结果
     */
    private boolean verify(String keyId, String keyVersionId, String alg, String digest, String signature) {
        return executeKmsOperation(new AsymmetricVerifyRequestWrapper(keyId, keyVersionId, alg, digest, signature));
    }

    /**
     * 加密请求包装器
     */
    private class EncryptRequestWrapper extends KmsRequestWrapper<String> {
        private final String plaintext;

        public EncryptRequestWrapper(String plaintext) {
            super("数据加密");
            this.plaintext = plaintext;
        }

        @Override
        protected Object buildRequest() {
            return new com.aliyun.kms20160120.models.EncryptRequest()
                    .setKeyId(keyId)
                    .setPlaintext(plaintext);
        }

        @Override
        protected String getMethodName() {
            return "encrypt";
        }

        @Override
        protected String processResponse(Object response) {
            com.aliyun.kms20160120.models.EncryptResponse encryptResponse = (com.aliyun.kms20160120.models.EncryptResponse) response;
            return encryptResponse.getBody().ciphertextBlob;
        }
    }

    /**
     * 解密请求包装器
     */
    private class DecryptRequestWrapper extends KmsRequestWrapper<String> {
        private final String ciphertext;

        public DecryptRequestWrapper(String ciphertext) {
            super("数据解密");
            this.ciphertext = ciphertext;
        }

        @Override
        protected Object buildRequest() {
            return new com.aliyun.kms20160120.models.DecryptRequest()
                    .setCiphertextBlob(ciphertext);
        }

        @Override
        protected String getMethodName() {
            return "decrypt";
        }

        @Override
        protected String processResponse(Object response) {
            com.aliyun.kms20160120.models.DecryptResponse decryptResponse = (com.aliyun.kms20160120.models.DecryptResponse) response;
            return decryptResponse.getBody().plaintext;
        }
    }

    /**
     * 生成数据密钥请求包装器
     */
    private class GenerateDataKeyRequestWrapper extends KmsRequestWrapper<DataKeyInfo> {
        private final String keySpec;

        public GenerateDataKeyRequestWrapper(String keySpec) {
            super("生成数据密钥");
            this.keySpec = keySpec;
        }

        @Override
        protected Object buildRequest() {
            return new com.aliyun.kms20160120.models.GenerateDataKeyRequest()
                    .setKeyId(keyId)
                    .setKeySpec(keySpec);
        }

        @Override
        protected String getMethodName() {
            return "generateDataKey";
        }

        @Override
        protected DataKeyInfo processResponse(Object response) {
            com.aliyun.kms20160120.models.GenerateDataKeyResponse dataKeyResponse = (com.aliyun.kms20160120.models.GenerateDataKeyResponse) response;
            return new DataKeyInfo(
                    dataKeyResponse.getBody().plaintext,
                    dataKeyResponse.getBody().ciphertextBlob);
        }
    }

    /**
     * KMS操作异常
     */
    public static class KmsOperationException extends RuntimeException {
        public KmsOperationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public String sign(Map<String, Object> claims) {
        String json = FastjsonUtil.toJson(claims);
        String payload = Base64.getUrlEncoder().withoutPadding().encodeToString(json.getBytes());
        return sign(payload);
    }

    public boolean verify(JwsHeader<?> header, String payload, String signature) {
        String keyId = header.getKeyId();
        String algorithm = header.getAlgorithm();
        String keyVersionId = (String) header.get("keyVersionId");
        return verify(keyId, keyVersionId, algorithm, payload, signature);
    }

    // ========== 扩展方法：支持其他KMS操作 ==========

    /**
     * 加密数据
     *
     * @param plaintext 明文数据
     * @return 加密后的数据
     */
    public String encrypt(String plaintext) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.EncryptRequest request = new com.aliyun.kms20160120.models.EncryptRequest()
                    .setKeyId(keyId)
                    .setPlaintext(plaintext);

            com.aliyun.kms20160120.models.EncryptResponse response = kmsClient.encrypt(request);
            return response.getBody().ciphertextBlob;
        }, "数据加密");
    }

    /**
     * 解密数据
     *
     * @param ciphertext 密文数据
     * @return 解密后的数据
     */
    public String decrypt(String ciphertext) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.DecryptRequest request = new com.aliyun.kms20160120.models.DecryptRequest()
                    .setCiphertextBlob(ciphertext);

            com.aliyun.kms20160120.models.DecryptResponse response = kmsClient.decrypt(request);
            return response.getBody().plaintext;
        }, "数据解密");
    }

    /**
     * 生成数据密钥
     *
     * @param keySpec 密钥规格
     * @return 数据密钥信息
     */
    public DataKeyInfo generateDataKey(String keySpec) {
        return executeKmsOperation(() -> {
            com.aliyun.kms20160120.models.GenerateDataKeyRequest request = new com.aliyun.kms20160120.models.GenerateDataKeyRequest()
                    .setKeyId(keyId)
                    .setKeySpec(keySpec);

            com.aliyun.kms20160120.models.GenerateDataKeyResponse response = kmsClient.generateDataKey(request);
            return new DataKeyInfo(
                    response.getBody().plaintext,
                    response.getBody().ciphertextBlob);
        }, "生成数据密钥");
    }

    /**
     * 数据密钥信息
     */
    public static class DataKeyInfo {
        private final String plaintextKey;
        private final String ciphertextBlob;

        public DataKeyInfo(String plaintextKey, String ciphertextBlob) {
            this.plaintextKey = plaintextKey;
            this.ciphertextBlob = ciphertextBlob;
        }

        public String getPlaintextKey() {
            return plaintextKey;
        }

        public String getCiphertextBlob() {
            return ciphertextBlob;
        }
    }
}